@echo off
cd app\src\main\jni\SDK

ren "无痕_Basic.cpp" "Basic.cpp"
ren "无痕_Basic.hpp" "Basic.hpp"
ren "无痕_Basic_classes.hpp" "Basic_classes.hpp"
ren "无痕_Basic_functions.cpp" "Basic_functions.cpp"
ren "无痕_Basic_parameters.hpp" "Basic_parameters.hpp"
ren "无痕_Basic_structs.hpp" "Basic_structs.hpp"
ren "无痕_Client_functions.cpp" "Client_functions.cpp"
ren "无痕_CoreUObject_functions.cpp" "CoreUObject_functions.cpp"
ren "无痕_Engine_functions.cpp" "Engine_functions.cpp"
ren "无痕_ShadowTrackerExtra_functions.cpp" "ShadowTrackerExtra_functions.cpp"

cd ..\..\..\..\..\

echo Files renamed successfully!
